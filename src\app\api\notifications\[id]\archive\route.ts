import { NextRequest, NextResponse } from 'next/server';
import { NotificationModel } from '@/lib/models/notification';
import { DocumentModel } from '@/lib/models/document';
import { archiveDocument } from '@/lib/database';
import { getUserFromSession } from '@/lib/auth-utils';

export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    
    // Get current user from session
    const currentUser = await getUserFromSession(request);
    if (!currentUser) {
      return NextResponse.json(
        { success: false, error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get notification data from database
    const notification = await NotificationModel.findById(id);

    if (!notification) {
      return NextResponse.json(
        { success: false, error: 'Notification not found' },
        { status: 404 }
      );
    }

    // Get the associated document to access PDF data
    if (!notification.documentId) {
      return NextResponse.json(
        { success: false, error: 'Notification has no associated document' },
        { status: 400 }
      );
    }

    const document = await DocumentModel.findById(notification.documentId);
    if (!document || !document.pdfData) {
      return NextResponse.json(
        { success: false, error: 'Associated document not found or has no data' },
        { status: 404 }
      );
    }

    // Parse PDF data from the document
    let pdfData;
    try {
      pdfData = typeof document.pdfData === 'string'
        ? JSON.parse(document.pdfData)
        : document.pdfData;
    } catch (parseError) {
      console.error('Error parsing document PDF data:', parseError);
      return NextResponse.json(
        { success: false, error: 'Invalid document data format' },
        { status: 400 }
      );
    }

    // Create complete notification data for archiving
    const userData = pdfData.userData || {};

    // Debug: Log the userData structure to understand the field names
    console.log('Archive API - userData structure:', JSON.stringify(userData, null, 2));
    console.log('Archive API - Available userData keys:', Object.keys(userData));

    // Extract name fields by searching through all userData keys
    const userDataKeys = Object.keys(userData);
    console.log('Archive API - All userData keys:', userDataKeys);

    // Find first name field
    const firstNameKey = userDataKeys.find(key => {
      const lowerKey = key.toLowerCase();
      return lowerKey.includes('first') && lowerKey.includes('name');
    });

    // Find last name field
    const lastNameKey = userDataKeys.find(key => {
      const lowerKey = key.toLowerCase();
      return (lowerKey.includes('last') && lowerKey.includes('name')) ||
             lowerKey.includes('surname') ||
             lowerKey === 'lastname';
    });

    // Find middle name/initial field
    const middleNameKey = userDataKeys.find(key => {
      const lowerKey = key.toLowerCase();
      return (lowerKey.includes('middle') && (lowerKey.includes('name') || lowerKey.includes('initial'))) ||
             lowerKey === 'middleinitial' ||
             lowerKey === 'middle_initial';
    });

    const firstName = firstNameKey ? userData[firstNameKey] : '';
    const lastName = lastNameKey ? userData[lastNameKey] : '';
    const middleInitial = middleNameKey ? userData[middleNameKey] : '';

    console.log('Archive API - Found name fields:', {
      firstNameKey,
      lastNameKey,
      middleNameKey,
      firstName,
      lastName,
      middleInitial
    });

    const applicantName = [firstName, middleInitial, lastName]
      .filter(name => name && name.trim())
      .join(' ')
      .trim() || 'Unknown Applicant';

    console.log('Archive API - Constructed applicant name:', applicantName);

    const notificationData = {
      id: id,
      title: notification.title,
      message: notification.message,
      templateName: pdfData.templateName || 'Unknown Template',
      templateId: pdfData.templateId || id,
      pdfData: pdfData,
      // Constructed applicant name
      applicantName: applicantName,
      // Extract user data from pdfData with fallbacks
      firstName: firstName,
      lastName: lastName,
      middleInitial: middleInitial,
      suffix: userData.suffix || '',
      // Additional metadata
      createdAt: notification.createdAt,
      userId: notification.userId,
    };

    // Debug: Log the notification data being archived
    console.log('Archiving notification data:', JSON.stringify(notificationData, null, 2));

    // Archive the document to SQLite
    const archivedDoc = await archiveDocument(notificationData, currentUser.username);

    // After successful archiving, delete the notification
    const deleteSuccess = await NotificationModel.delete(id);

    if (!deleteSuccess) {
      console.warn('Failed to delete notification after archiving, but archive was successful');
    }

    return NextResponse.json({
      success: true,
      message: 'Document archived successfully',
      data: {
        archivedId: archivedDoc.id,
        archivedAt: archivedDoc.approved_at
      }
    });

  } catch (error) {
    console.error('Archive document error:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to archive document' },
      { status: 500 }
    );
  }
}
